<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\RequestDelete\Jobs\DomainEppCancellation;
use App\Mail\UserDeleteRequestMail;
use App\Modules\Client\Constants\DomainStatus;
use App\Events\DomainHistoryEvent;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class DomainDeleteService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }
    public function createDeleteRequest($requestOrData)
    {
        $data = is_array($requestOrData) ? $requestOrData : $requestOrData->all();
        return $this->processCreateRequest($data);
    }

    public function approveDeleteRequest($requestOrData)
    {
        $data = is_array($requestOrData) ? $requestOrData : $requestOrData->all();
        return $this->processApproveRequest($data);
    }

    public function rejectDeleteRequest($requestOrData)
    {
        $data = is_array($requestOrData) ? $requestOrData : $requestOrData->all();

        if (!isset($data['domainName']) || !isset($data['userID'])) {
            $domainInfo = DatabaseQueryService::instance()->getDomainInfo($data['domainId']);
            $data['domainName'] = $domainInfo->domainName;
            $data['userID'] = $domainInfo->userID;
            $data['userEmail'] = $domainInfo->userEmail;
            $data['reason'] = $domainInfo->reason ?? 'Domain deletion request';
        }

        return $this->processRejectRequest($data);
    }

    public function cancelDeleteRequest($requestOrData)
    {
        $data = is_array($requestOrData) ? $requestOrData : $requestOrData->all();

        if (empty($data['support_note'])) {
            throw new \Exception("Support note is required when cancelling a domain deletion request.");
        }

        return $this->processCancelRequest($data);
    }

    public function approveDeleteRequestSave($requestOrData)
    {
        return $this->approveDeleteRequest($requestOrData);
    }

    public function rejectDeleteRequestSave($request)
    {
        return $this->rejectDeleteRequest($request);
    }

    public function createDeleteRequestSave($request)
    {
        return $this->createDeleteRequest($request);
    }

    public function cancelDeleteRequestSave($requestOrData)
    {
        return $this->cancelDeleteRequest($requestOrData);
    }

    public function processExpiredRequests()
    {
        $approvedRequests = DB::client()->table('domain_cancellation_requests')
            ->join('domains', 'domain_cancellation_requests.domain_id', '=', 'domains.id')
            ->join('users', 'domain_cancellation_requests.user_id', '=', 'users.id')
            ->select([
                'domain_cancellation_requests.domain_id as domainId',
                'domains.name as domainName',
                'domains.status as domainStatus',
                'domain_cancellation_requests.user_id as userID',
                'users.email as userEmail',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.feedback_date as approvedDate',
                'domain_cancellation_requests.support_agent_id',
                'domain_cancellation_requests.support_agent_name'
            ])
            ->where('domains.status', 'IN_PROCESS')
            ->whereNotNull('domain_cancellation_requests.support_agent_id')
            ->whereNotNull('domain_cancellation_requests.deleted_at')
            ->whereNotNull('domain_cancellation_requests.feedback_date')
            ->where('domain_cancellation_requests.feedback_date', '<=', now()->subHours(24))
            ->get();

        foreach ($approvedRequests as $request) {
            $data = [
                'domainId' => $request->domainId,
                'domainName' => $request->domainName,
                'userId' => $request->userID,
                'userEmail' => $request->userEmail,
                'reason' => $request->reason,
                'createdDate' => $request->approvedDate,
                'adminId' => $request->support_agent_id,
                'adminName' => 'System',
                'adminEmail' => '<EMAIL>',
                'supportNote' => "Deletion processed automatically after 24+ hours"
            ];

            $this->processApprovedRequest($data);
        }

        return count($approvedRequests);
    }

    private function processCreateRequest(array $data): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = "Request delete created by {$adminContext['email']}";

        $this->updateDomainStatusDeletion($data['domainId']);
        $this->createDomainCancellationRequest($data, $adminContext, $supportNote);
        $this->sendUserNotification($data, 'Domain Deletion Request Created', 'A deletion request for your domain "' . $data['domainName'] . '" has been created by admin and approved. The deletion process will take 1-2 days to complete. This action is final and cannot be undone.');
        $this->sendUserEmail($data, 'Domain Deletion Request Created', 'A deletion request for your domain "' . $data['domainName'] . '" has been created by admin and approved. The deletion process will take 1-2 days to complete. This action is final and cannot be undone.');
    }

    private function processApproveRequest(array $data): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = "Request delete approved by {$adminContext['email']}";

        $this->logDomainHistory($data, 'DOMAIN_UPDATED', 'success', 'Domain deletion request approved by ' . $adminContext['name'] . ' (' . $adminContext['email'] . ')');

        $this->markAsApproved($data, $adminContext, $supportNote);
        $this->sendUserNotification($data, 'Domain Deletion Request Approved','Your request to delete the domain "' . $data['domainName'] . '" has been approved. The deletion process will take 1-2 days to complete.');
        $this->sendUserEmail($data, 'Domain Deletion Request Approved', 'Your request to delete the domain "' . $data['domainName'] . '" has been approved. The deletion process will take 1-2 days to complete. This action is final and cannot be undone.');
    }

    private function processRejectRequest(array $data): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = $data['support_note'] ?? "Request delete rejected by {$adminContext['email']}";

        $this->rejectDomainDeletionRequest($data, $adminContext, $supportNote);
        $this->reactivateDomain($data['domainId']);
        $this->sendRejectionNotification($data, $adminContext);
        $this->logDomainRejectionHistory($data, $adminContext);
    }

    private function processCancelRequest(array $data): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = $data['support_note'];

        $this->logDomainHistory($data, 'DOMAIN_UPDATED', 'success','Domain deletion request cancelled by ' . $adminContext['name'] . ' (' . $adminContext['email'] . ')');

        $this->cancelDomainDeletionRequest($data, $adminContext, $supportNote);
        $this->reactivateDomain($data['domainId']);
        $this->sendCancellationNotification($data, $adminContext);
    }

    private function processApprovedRequest(array $data): void
    {
        DomainEppCancellation::dispatch(
            $data['domainId'],
            $data['domainName'],
            $data['userId'],
            $data['userEmail'],
            $data['reason'] ?? 'Domain deletion request',
            $data['createdDate'] ?? now()->toDateTimeString(),
            $data['supportNote'],
            $data['adminId'],
            $data['adminName'],
            $data['adminEmail']
        );
    }

    private function getAdminContext(): array
    {
        return [
            'id' => Auth::id() ?? 1,
            'name' => Auth::user()->name ?? 'System',
            'email' => Auth::user()->email ?? '<EMAIL>'
        ];
    }

    private function updateDomainStatusDeletion($domainId): void
    {
        DB::client()->table('domains')
            ->where('id', $domainId)
            ->update([
                'status' => DomainStatus::IN_PROCESS,
                'updated_at' => now(),
            ]);
    }

    private function reactivateDomain($domainId): void
    {
        DB::client()->table('domains')
            ->where('id', $domainId)
            ->update([
                'status' => DomainStatus::ACTIVE,
                'updated_at' => now(),
            ]);
    }

    private function createDomainCancellationRequest(array $data, array $adminContext, string $supportNote): void
    {
        $adminFullName = "{$adminContext['name']} ({$adminContext['email']})";

        DB::client()->table('domain_cancellation_requests')->insert([
            'user_id'             => $data['userID'],
            'domain_id'           => $data['domainId'],
            'reason'              => $data['reason'] ?? 'Domain deletion request by ' . $adminFullName,
            'requested_at'        => now(),
            'support_agent_id'    => $adminContext['id'],
            'support_agent_name'  => $adminFullName,
            'feedback_date'       => now(),
            'support_note'        => $supportNote,
            'deleted_at'          => now(),
            'is_refunded'         => false,
        ]);
    }

    private function markAsApproved(array $data, array $adminContext, string $supportNote): void
    {
        $date = Carbon::parse($data['createdDate'] ?? now());
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;
        $adminFullName = "{$adminContext['name']} ({$adminContext['email']})";

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $data['domainId'])
            ->update([
                'support_agent_id'   => $adminContext['id'],
                'support_agent_name' => $adminFullName,
                'feedback_date'      => now(),
                'support_note'       => $supportNote,
                'is_refunded'        => $is_refunded,
                'deleted_at'         => now(),
            ]);
    }

    private function rejectDomainDeletionRequest(array $data, array $adminContext, string $supportNote): void
    {
        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $data['domainId'])
            ->update([
                'support_agent_id' => $adminContext['id'],
                'support_agent_name' => $adminContext['name'] . ' (' . $adminContext['email'] . ')',
                'support_note' => $supportNote,
                'feedback_date' => now(),
            ]);
    }

    private function cancelDomainDeletionRequest(array $data, array $adminContext, string $supportNote): void
    {
        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $data['domainId'])
            ->update([
                'support_agent_id' => $adminContext['id'],
                'support_agent_name' => $adminContext['name'] . ' (' . $adminContext['email'] . ')',
                'support_note' => $supportNote,
                'feedback_date' => now(),
                'deleted_at' => null,
            ]);
    }

    private function sendUserNotification(array $data, string $title, string $message): void
    {
        if (!isset($data['userID']) || !isset($data['domainName'])) return;

        DB::client()->table('notifications')->insert([
            'user_id'      => $data['userID'],
            'title'        => $title,
            'message'      => $message,
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'updated_at'   => now(),
            'importance'   => 'important',
        ]);
    }

    private function sendUserEmail(array $data, string $subject, string $body): void
    {
        if (!isset($data['userEmail']) || !isset($data['domainName'])) return;

        $message = [
            'subject'  => $subject,
            'greeting' => 'Greetings!',
            'body'     => $body,
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($data['userEmail'])->send(new UserDeleteRequestMail($message));
        $this->emailTrack($data['userEmail'], $message, $data['domainId']);
    }

    private function sendRejectionNotification(array $data, array $adminContext): void
    {
        $message = 'Your request to delete the domain "' . $data['domainName'] . '" has been rejected by ' . $adminContext['name'] . '.';

        $this->sendUserNotification($data, 'Domain Deletion Request Rejected', $message);

        $emailBody = 'Your request to delete the domain "' . $data['domainName'] . '" has been rejected by our support team. If you have any questions, please contact our support.';
        $this->sendUserEmail($data, 'Domain Deletion Request Rejected', $emailBody);
    }

    private function sendCancellationNotification(array $data, array $adminContext): void
    {
        $message = 'Your domain deletion request for "' . $data['domainName'] . '" has been cancelled by ' . $adminContext['name'] . '.';

        $this->sendUserNotification($data, 'Domain Deletion Request Cancelled', $message);

        $emailBody = 'Your domain deletion request for "' . $data['domainName'] . '" has been cancelled by our support team. Your domain remains active.';
        $this->sendUserEmail($data, 'Domain Deletion Request Cancelled', $emailBody);
    }

    private function emailTrack(string $userEmail, array $message, int $domainId): void
    {
        DB::client()->table('email_tracks')->insert([
            'email'      => $userEmail,
            'subject'    => $message['subject'],
            'body'       => $message['body'],
            'domain_id'  => $domainId,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    private function logDomainHistory(array $data, string $type, string $status, string $message): void
    {
        event(new DomainHistoryEvent(['domain_id' => $data['domainId'],'type' => $type,'status' => $status,'user_id' => $data['userID'] ?? null ,'message' => $message,'payload' => json_encode($data),]));
    }

    private function logDomainRejectionHistory(array $data, array $adminContext): void
    {
        $this->logDomainHistory($data, 'DOMAIN_UPDATED', 'success',
            'Domain deletion request rejected by ' . $adminContext['name'] . ' (' . $adminContext['email'] . ')');
    }
}
