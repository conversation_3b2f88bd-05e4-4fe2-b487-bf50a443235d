<?php

namespace App\Modules\RequestDelete\Requests;

use App\Modules\RequestDelete\Services\DatabaseQueryService;
use App\Modules\RequestDelete\Services\DomainDeleteService;
use Illuminate\Foundation\Http\FormRequest;

class ApproveDeleteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'domainId' => ['required', 'integer'],
            'support_note' => ['string', 'min:10'],
        ];
    }

    public function approve()
    {
        return DomainDeleteService::instance()->processApproveRequest($this);
    }
}
